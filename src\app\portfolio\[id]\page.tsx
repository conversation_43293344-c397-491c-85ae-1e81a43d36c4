import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { notFound } from "next/navigation";

// 模拟项目数据
const projects = [
  {
    id: 1,
    title: "电子商务平台",
    description: "基于Next.js和Stripe构建的全功能电商网站",
    image: "/placeholder.svg",
    tags: ["Next.js", "React", "Stripe", "Tailwind CSS"],
    content: `
      <p>这是一个使用Next.js和Stripe构建的全功能电子商务平台。该项目旨在提供现代、高性能的购物体验，同时展示我在前端和后端开发方面的技能。</p>
      
      <h2>项目特点</h2>
      <ul>
        <li>响应式设计，适配各种设备</li>
        <li>产品目录和详情页面</li>
        <li>购物车和结账流程</li>
        <li>用户账户和订单历史</li>
        <li>与Stripe集成的安全支付处理</li>
        <li>管理员仪表板用于产品和订单管理</li>
      </ul>
      
      <h2>技术栈</h2>
      <ul>
        <li>Next.js用于前端和API路由</li>
        <li>React用于用户界面</li>
        <li>Tailwind CSS用于样式</li>
        <li>Stripe用于支付处理</li>
        <li>PostgreSQL用于数据存储</li>
        <li>Prisma用于数据库访问</li>
        <li>NextAuth.js用于身份验证</li>
      </ul>
      
      <h2>开发挑战</h2>
      <p>在开发这个项目的过程中，我面临了几个挑战：</p>
      <ul>
        <li>实现安全的支付处理流程</li>
        <li>优化产品图像的加载性能</li>
        <li>设计直观的购物体验</li>
        <li>确保网站在各种设备上的响应性</li>
      </ul>
      
      <h2>解决方案</h2>
      <p>我通过以下方式解决了这些挑战：</p>
      <ul>
        <li>使用Stripe的安全API和webhooks处理支付</li>
        <li>利用Next.js的Image组件和自动图像优化</li>
        <li>进行用户测试和迭代设计改进</li>
        <li>采用移动优先的设计方法和Tailwind CSS的响应式工具</li>
      </ul>
      
      <h2>成果</h2>
      <p>该项目成功地展示了一个现代电子商务平台的核心功能，同时保持了出色的性能和用户体验。它展示了我在前端和后端开发、支付集成和响应式设计方面的技能。</p>
    `,
    demoUrl: "https://example.com/demo",
    githubUrl: "https://github.com/example/project"
  },
  {
    id: 2,
    title: "任务管理应用",
    description: "帮助团队协作和任务跟踪的Web应用",
    image: "/placeholder.svg",
    tags: ["React", "Firebase", "Tailwind CSS"],
    content: `
      <p>这是一个使用React和Firebase构建的任务管理应用，旨在帮助团队更有效地协作和跟踪任务。该项目展示了我在前端开发和实时数据处理方面的技能。</p>
      
      <h2>项目特点</h2>
      <ul>
        <li>用户友好的任务创建和管理界面</li>
        <li>项目和团队组织功能</li>
        <li>任务分配和截止日期跟踪</li>
        <li>实时更新和通知</li>
        <li>评论和协作工具</li>
        <li>数据可视化和报告</li>
      </ul>
      
      <h2>技术栈</h2>
      <ul>
        <li>React用于用户界面</li>
        <li>Firebase用于后端和实时数据库</li>
        <li>Tailwind CSS用于样式</li>
        <li>Firebase Authentication用于用户认证</li>
        <li>Cloud Functions用于后端逻辑</li>
        <li>React DnD用于拖放功能</li>
      </ul>
      
      <h2>开发挑战</h2>
      <p>在开发这个项目的过程中，我面临了几个挑战：</p>
      <ul>
        <li>实现高效的实时数据同步</li>
        <li>设计直观的任务管理界面</li>
        <li>处理复杂的任务关系和依赖</li>
        <li>确保应用在高负载下的性能</li>
      </ul>
      
      <h2>解决方案</h2>
      <p>我通过以下方式解决了这些挑战：</p>
      <ul>
        <li>利用Firebase的实时数据库功能</li>
        <li>进行用户研究和迭代UI设计</li>
        <li>实现自定义数据结构来处理任务关系</li>
        <li>优化查询和数据缓存策略</li>
      </ul>
      
      <h2>成果</h2>
      <p>该应用成功地提供了一个直观、高效的任务管理解决方案，得到了用户的积极反馈。它展示了我在构建复杂、交互式Web应用方面的能力。</p>
    `,
    demoUrl: "https://example.com/demo",
    githubUrl: "https://github.com/example/project"
  },
  {
    id: 3,
    title: "个人博客平台",
    description: "使用Next.js和MDX构建的内容管理系统",
    image: "/placeholder.svg",
    tags: ["Next.js", "MDX", "Tailwind CSS"],
    content: `
      <p>这是一个使用Next.js和MDX构建的个人博客平台，旨在提供一个现代、高性能的内容管理系统。该项目展示了我在静态站点生成和内容管理方面的技能。</p>
      
      <h2>项目特点</h2>
      <ul>
        <li>基于MDX的内容创作系统</li>
        <li>自动生成的文章目录</li>
        <li>标签和分类功能</li>
        <li>响应式设计</li>
        <li>深色模式支持</li>
        <li>SEO优化</li>
        <li>评论系统集成</li>
      </ul>
      
      <h2>技术栈</h2>
      <ul>
        <li>Next.js用于静态站点生成</li>
        <li>MDX用于内容管理</li>
        <li>Tailwind CSS用于样式</li>
        <li>next-mdx-remote用于MDX处理</li>
        <li>Prism.js用于代码高亮</li>
        <li>next-seo用于SEO优化</li>
      </ul>
      
      <h2>开发挑战</h2>
      <p>在开发这个项目的过程中，我面临了几个挑战：</p>
      <ul>
        <li>创建灵活的MDX内容系统</li>
        <li>优化图像和页面加载性能</li>
        <li>实现高效的内容搜索功能</li>
        <li>确保良好的SEO实践</li>
      </ul>
      
      <h2>解决方案</h2>
      <p>我通过以下方式解决了这些挑战：</p>
      <ul>
        <li>开发自定义MDX组件和处理流程</li>
        <li>使用Next.js的图像优化和增量静态再生</li>
        <li>实现基于Algolia的搜索功能</li>
        <li>应用结构化数据和其他SEO最佳实践</li>
      </ul>
      
      <h2>成果</h2>
      <p>该平台成功地提供了一个高性能、易于使用的博客系统，具有出色的内容创作体验和读者体验。它展示了我在构建现代内容管理系统方面的能力。</p>
    `,
    demoUrl: "https://example.com/demo",
    githubUrl: "https://github.com/example/project"
  },
  {
    id: 4,
    title: "天气预报应用",
    description: "使用OpenWeather API的实时天气应用",
    image: "/placeholder.svg",
    tags: ["React", "API集成", "CSS"],
    content: `
      <p>这是一个使用React和OpenWeather API构建的实时天气预报应用。该项目旨在提供直观、美观的天气信息，同时展示我在API集成和数据可视化方面的技能。</p>
      
      <h2>项目特点</h2>
      <ul>
        <li>实时天气数据显示</li>
        <li>基于位置的天气预报</li>
        <li>5天天气预报</li>
        <li>温度、湿度、风速等详细信息</li>
        <li>天气变化的可视化图表</li>
        <li>响应式设计，适配各种设备</li>
      </ul>
      
      <h2>技术栈</h2>
      <ul>
        <li>React用于用户界面</li>
        <li>OpenWeather API用于天气数据</li>
        <li>Axios用于API请求</li>
        <li>Chart.js用于数据可视化</li>
        <li>CSS用于自定义样式</li>
        <li>Geolocation API用于位置检测</li>
      </ul>
      
      <h2>开发挑战</h2>
      <p>在开发这个项目的过程中，我面临了几个挑战：</p>
      <ul>
        <li>处理API请求和错误</li>
        <li>设计直观的天气数据展示</li>
        <li>实现准确的位置检测</li>
        <li>优化移动设备上的用户体验</li>
      </ul>
      
      <h2>解决方案</h2>
      <p>我通过以下方式解决了这些挑战：</p>
      <ul>
        <li>实现健壮的API错误处理和重试机制</li>
        <li>创建自定义天气图标和数据可视化</li>
        <li>结合IP定位和浏览器Geolocation API</li>
        <li>采用移动优先的设计方法</li>
      </ul>
      
      <h2>成果</h2>
      <p>该应用成功地提供了一个用户友好、信息丰富的天气预报体验。它展示了我在API集成、数据处理和前端设计方面的技能。</p>
    `,
    demoUrl: "https://example.com/demo",
    githubUrl: "https://github.com/example/project"
  }
];

export async function generateMetadata({ params }: { params: { id: string } }) {
  const project = projects.find(project => project.id === parseInt(params.id));
  
  if (!project) {
    return {
      title: "项目未找到",
      description: "抱歉，您请求的项目不存在。",
    };
  }
  
  return {
    title: `${project.title} | 个人博客与作品集`,
    description: project.description,
  };
}

export default function ProjectPage({ params }: { params: { id: string } }) {
  const project = projects.find(project => project.id === parseInt(params.id));
  
  if (!project) {
    notFound();
  }
  
  return (
    <div className="container py-8 md:py-12 max-w-4xl">
      <Button asChild variant="ghost" className="mb-6">
        <Link href="/portfolio">← 返回作品集</Link>
      </Button>
      
      <div className="aspect-video relative bg-muted mb-8 rounded-lg overflow-hidden">
        <Image 
          src={project.image} 
          alt={project.title}
          fill 
          className="object-cover" 
        />
      </div>
      
      <div className="mb-8">
        <h1 className="text-4xl font-bold tracking-tight mb-2">{project.title}</h1>
        <p className="text-xl text-muted-foreground mb-4">{project.description}</p>
        
        <div className="flex flex-wrap gap-2 mb-6">
          {project.tags.map((tag) => (
            <span key={tag} className="inline-flex items-center rounded-md bg-muted px-2 py-1 text-xs font-medium">
              {tag}
            </span>
          ))}
        </div>
        
        <div className="flex gap-4">
          <Button asChild>
            <a href={project.demoUrl} target="_blank" rel="noopener noreferrer">查看演示</a>
          </Button>
          <Button asChild variant="outline">
            <a href={project.githubUrl} target="_blank" rel="noopener noreferrer">GitHub 仓库</a>
          </Button>
        </div>
      </div>
      
      <article className="prose prose-neutral dark:prose-invert max-w-none">
        <div dangerouslySetInnerHTML={{ __html: project.content }} />
      </article>
    </div>
  );
}
