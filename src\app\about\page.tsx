import Image from "next/image";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export const metadata = {
  title: "关于我 | 个人博客与作品集",
  description: "了解更多关于我的背景、技能和经验",
};

export default function AboutPage() {
  return (
    <div className="container py-8 md:py-12 space-y-12">
      <div className="flex flex-col md:flex-row gap-8 items-center md:items-start">
        <div className="w-40 h-40 rounded-full overflow-hidden flex-shrink-0">
          <Avatar className="w-40 h-40">
            <AvatarImage src="/placeholder.svg" alt="个人头像" />
            <AvatarFallback>CN</AvatarFallback>
          </Avatar>
        </div>
        <div className="space-y-4 text-center md:text-left">
          <h1 className="text-4xl font-bold tracking-tight">关于我</h1>
          <p className="text-xl text-muted-foreground">
            Web开发者 | UI设计师 | 内容创作者
          </p>
          <p className="max-w-[700px]">
            我是一名热爱创造的Web开发者，专注于构建美观、高效且用户友好的网站和应用程序。
            我擅长使用现代前端技术栈，包括React、Next.js和Tailwind CSS，为用户提供卓越的数字体验。
          </p>
        </div>
      </div>

      <section className="space-y-4">
        <h2 className="text-2xl font-bold tracking-tight">技能</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          <div className="border rounded-lg p-4 space-y-2">
            <h3 className="font-semibold">前端开发</h3>
            <ul className="list-disc list-inside text-muted-foreground">
              <li>React & Next.js</li>
              <li>TypeScript</li>
              <li>Tailwind CSS</li>
              <li>响应式设计</li>
            </ul>
          </div>
          <div className="border rounded-lg p-4 space-y-2">
            <h3 className="font-semibold">后端开发</h3>
            <ul className="list-disc list-inside text-muted-foreground">
              <li>Node.js</li>
              <li>Express</li>
              <li>API开发</li>
              <li>数据库设计</li>
            </ul>
          </div>
          <div className="border rounded-lg p-4 space-y-2">
            <h3 className="font-semibold">设计</h3>
            <ul className="list-disc list-inside text-muted-foreground">
              <li>UI/UX设计</li>
              <li>Figma</li>
              <li>原型设计</li>
              <li>用户体验研究</li>
            </ul>
          </div>
        </div>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-bold tracking-tight">经历</h2>
        <div className="space-y-6">
          <div className="border-l-2 pl-4 space-y-1">
            <h3 className="font-semibold">高级前端开发工程师</h3>
            <p className="text-sm text-muted-foreground">某科技公司 | 2021 - 至今</p>
            <p className="text-muted-foreground">
              负责公司核心产品的前端架构设计和开发，优化用户体验和性能。
            </p>
          </div>
          <div className="border-l-2 pl-4 space-y-1">
            <h3 className="font-semibold">Web开发工程师</h3>
            <p className="text-sm text-muted-foreground">某互联网公司 | 2018 - 2021</p>
            <p className="text-muted-foreground">
              参与多个Web应用的开发，负责前端实现和与后端的集成。
            </p>
          </div>
          <div className="border-l-2 pl-4 space-y-1">
            <h3 className="font-semibold">自由职业开发者</h3>
            <p className="text-sm text-muted-foreground">2016 - 2018</p>
            <p className="text-muted-foreground">
              为多个客户提供网站开发和设计服务，积累了丰富的项目经验。
            </p>
          </div>
        </div>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-bold tracking-tight">联系方式</h2>
        <p>
          如果您对我的工作感兴趣，或者有任何合作机会，欢迎通过以下方式联系我：
        </p>
        <div className="flex flex-col sm:flex-row gap-4">
          <a href="mailto:<EMAIL>" className="inline-flex items-center gap-2 text-primary hover:underline">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-mail">
              <rect width="20" height="16" x="2" y="4" rx="2" />
              <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
            </svg>
            <EMAIL>
          </a>
          <a href="https://github.com" target="_blank" rel="noreferrer" className="inline-flex items-center gap-2 text-primary hover:underline">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-github">
              <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4" />
              <path d="M9 18c-4.51 2-5-2-7-2" />
            </svg>
            GitHub
          </a>
          <a href="https://linkedin.com" target="_blank" rel="noreferrer" className="inline-flex items-center gap-2 text-primary hover:underline">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-linkedin">
              <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
              <rect width="4" height="12" x="2" y="9" />
              <circle cx="4" cy="4" r="2" />
            </svg>
            LinkedIn
          </a>
        </div>
      </section>
    </div>
  );
}
