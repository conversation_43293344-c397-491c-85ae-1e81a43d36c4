import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

// 模拟博客文章数据
const blogPosts = [
  {
    id: 1,
    title: "创建响应式网站的最佳实践",
    description: "学习如何使用现代技术栈构建响应式网站",
    date: "2024-04-20",
    category: "Web开发"
  },
  {
    id: 2,
    title: "Next.js 14的新特性",
    description: "探索Next.js最新版本中的新功能和改进",
    date: "2024-04-15",
    category: "前端框架"
  },
  {
    id: 3,
    title: "使用Tailwind CSS提高开发效率",
    description: "如何利用Tailwind CSS加速UI开发过程",
    date: "2024-04-10",
    category: "CSS"
  },
  {
    id: 4,
    title: "React Server Components简介",
    description: "了解React Server Components如何改变前端开发",
    date: "2024-04-05",
    category: "React"
  },
  {
    id: 5,
    title: "现代Web应用中的状态管理",
    description: "比较不同的状态管理解决方案及其适用场景",
    date: "2024-04-01",
    category: "前端架构"
  },
  {
    id: 6,
    title: "Web性能优化技巧",
    description: "提高网站加载速度和用户体验的实用技巧",
    date: "2024-03-25",
    category: "性能优化"
  }
];

export const metadata = {
  title: "博客 | 个人博客与作品集",
  description: "浏览我的最新博客文章，了解Web开发的最新趋势和技术",
};

export default function BlogPage() {
  return (
    <div className="container py-8 md:py-12 space-y-8">
      <div className="space-y-4 text-center">
        <h1 className="text-4xl font-bold tracking-tight">博客</h1>
        <p className="text-xl text-muted-foreground max-w-[700px] mx-auto">
          分享我在Web开发领域的经验、教程和见解
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {blogPosts.map((post) => (
          <Card key={post.id} className="overflow-hidden">
            <CardHeader>
              <div className="text-sm text-muted-foreground">{post.date} · {post.category}</div>
              <CardTitle className="line-clamp-2">{post.title}</CardTitle>
              <CardDescription className="line-clamp-2">{post.description}</CardDescription>
            </CardHeader>
            <CardFooter>
              <Button asChild variant="ghost" className="p-0">
                <Link href={`/blog/${post.id}`}>阅读全文 →</Link>
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}
