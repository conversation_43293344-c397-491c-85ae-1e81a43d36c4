import Link from "next/link";
import { Button } from "@/components/ui/button";
import { notFound } from "next/navigation";

// 模拟博客文章数据
const blogPosts = [
  {
    id: 1,
    title: "创建响应式网站的最佳实践",
    description: "学习如何使用现代技术栈构建响应式网站",
    date: "2024-04-20",
    category: "Web开发",
    content: `
      <p>在当今的数字世界中，响应式网站设计已经成为标准。无论用户使用什么设备访问您的网站，都应该获得最佳的浏览体验。本文将分享一些创建响应式网站的最佳实践。</p>
      
      <h2>使用灵活的网格布局</h2>
      <p>响应式设计的基础是灵活的网格布局。使用百分比而不是固定像素值来定义元素的宽度，可以让您的布局自动适应不同的屏幕尺寸。</p>
      
      <h2>媒体查询的有效使用</h2>
      <p>CSS媒体查询允许您根据设备特性（如屏幕宽度、高度或方向）应用不同的样式。合理使用媒体查询可以确保您的网站在各种设备上都能完美展示。</p>
      
      <h2>优先考虑移动设备</h2>
      <p>采用"移动优先"的设计方法，首先为移动设备设计网站，然后再逐步增强为更大屏幕的体验。这种方法可以确保您的网站在所有设备上都能良好运行。</p>
      
      <h2>使用现代CSS框架</h2>
      <p>像Tailwind CSS这样的现代CSS框架提供了强大的工具来创建响应式设计。它们包含预定义的类和组件，可以大大简化响应式网站的开发过程。</p>
      
      <h2>图像和媒体的响应式处理</h2>
      <p>确保您的图像和视频能够适应不同的屏幕尺寸。使用CSS的max-width属性或者像Next.js的Image组件这样的工具可以帮助您实现这一点。</p>
      
      <h2>测试不同设备</h2>
      <p>在开发过程中，定期在不同的设备和浏览器上测试您的网站。这可以帮助您发现并解决可能出现的问题。</p>
      
      <h2>结论</h2>
      <p>创建响应式网站需要综合考虑多种因素，但通过遵循这些最佳实践，您可以确保您的网站在任何设备上都能提供出色的用户体验。</p>
    `
  },
  {
    id: 2,
    title: "Next.js 14的新特性",
    description: "探索Next.js最新版本中的新功能和改进",
    date: "2024-04-15",
    category: "前端框架",
    content: `
      <p>Next.js 14带来了许多令人兴奋的新特性和改进，进一步增强了这个流行的React框架的功能。本文将探讨Next.js 14中的一些主要更新。</p>
      
      <h2>Server Components</h2>
      <p>Next.js 14进一步完善了React Server Components的实现，使开发者能够更轻松地构建高性能的应用程序。服务器组件允许在服务器上渲染React组件，减少发送到客户端的JavaScript数量。</p>
      
      <h2>Turbopack改进</h2>
      <p>Turbopack是Next.js的新一代打包工具，在Next.js 14中得到了显著改进。它提供了更快的开发体验和构建时间，特别是对于大型应用程序。</p>
      
      <h2>增强的路由系统</h2>
      <p>Next.js 14改进了App Router，提供了更灵活和直观的路由体验。新的路由系统支持嵌套布局、加载状态和错误处理等功能。</p>
      
      <h2>改进的图像优化</h2>
      <p>Next.js的Image组件在新版本中得到了增强，提供了更好的性能和用户体验。它现在支持更多的图像格式和优化选项。</p>
      
      <h2>更好的TypeScript集成</h2>
      <p>Next.js 14提供了更好的TypeScript支持，包括改进的类型定义和更好的开发工具集成。这使得使用TypeScript开发Next.js应用程序变得更加容易和高效。</p>
      
      <h2>结论</h2>
      <p>Next.js 14带来了许多令人兴奋的新特性和改进，使其成为构建现代Web应用程序的更强大工具。无论您是Next.js的新手还是经验丰富的开发者，这些更新都值得探索和利用。</p>
    `
  },
  {
    id: 3,
    title: "使用Tailwind CSS提高开发效率",
    description: "如何利用Tailwind CSS加速UI开发过程",
    date: "2024-04-10",
    category: "CSS",
    content: `
      <p>Tailwind CSS已经成为前端开发中最受欢迎的CSS框架之一，它通过提供实用优先的类名系统彻底改变了我们构建用户界面的方式。本文将探讨如何利用Tailwind CSS提高开发效率。</p>
      
      <h2>实用优先的方法</h2>
      <p>Tailwind CSS的核心理念是实用优先。它提供了大量预定义的实用类，您可以直接在HTML中应用这些类，而不需要编写自定义CSS。这种方法可以显著加快开发速度。</p>
      
      <h2>减少上下文切换</h2>
      <p>使用Tailwind CSS，您可以在不离开HTML的情况下设计界面。这减少了在HTML和CSS文件之间切换的需要，从而提高了开发效率。</p>
      
      <h2>响应式设计变得简单</h2>
      <p>Tailwind CSS提供了直观的响应式设计系统。通过使用像sm:、md:、lg:这样的前缀，您可以轻松地为不同的屏幕尺寸应用不同的样式。</p>
      
      <h2>主题定制</h2>
      <p>尽管Tailwind CSS提供了预定义的类，但它也非常灵活。您可以通过配置文件自定义颜色、间距、字体等，使框架适应您的设计需求。</p>
      
      <h2>与组件库集成</h2>
      <p>Tailwind CSS可以与像shadcn/ui这样的组件库完美集成，让您能够构建复杂的界面，同时保持代码的可维护性。</p>
      
      <h2>结论</h2>
      <p>Tailwind CSS通过其实用优先的方法、减少上下文切换、简化响应式设计和提供灵活的定制选项，显著提高了UI开发的效率。如果您还没有尝试过Tailwind CSS，现在是时候了解它如何改变您的开发工作流程了。</p>
    `
  }
];

export async function generateMetadata({ params }: { params: { id: string } }) {
  const post = blogPosts.find(post => post.id === parseInt(params.id));
  
  if (!post) {
    return {
      title: "文章未找到",
      description: "抱歉，您请求的文章不存在。",
    };
  }
  
  return {
    title: `${post.title} | 个人博客与作品集`,
    description: post.description,
  };
}

export default function BlogPostPage({ params }: { params: { id: string } }) {
  const post = blogPosts.find(post => post.id === parseInt(params.id));
  
  if (!post) {
    notFound();
  }
  
  return (
    <div className="container py-8 md:py-12 max-w-4xl">
      <Button asChild variant="ghost" className="mb-6">
        <Link href="/blog">← 返回博客列表</Link>
      </Button>
      
      <article className="prose prose-neutral dark:prose-invert max-w-none">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-2">{post.title}</h1>
          <div className="text-muted-foreground">
            {post.date} · {post.category}
          </div>
        </div>
        
        <div dangerouslySetInnerHTML={{ __html: post.content }} />
      </article>
    </div>
  );
}
