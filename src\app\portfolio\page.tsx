import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

// 模拟项目数据
const projects = [
  {
    id: 1,
    title: "电子商务平台",
    description: "基于Next.js和Stripe构建的全功能电商网站",
    image: "/placeholder.svg",
    tags: ["Next.js", "React", "Stripe", "Tailwind CSS"]
  },
  {
    id: 2,
    title: "任务管理应用",
    description: "帮助团队协作和任务跟踪的Web应用",
    image: "/placeholder.svg",
    tags: ["React", "Firebase", "Tailwind CSS"]
  },
  {
    id: 3,
    title: "个人博客平台",
    description: "使用Next.js和MDX构建的内容管理系统",
    image: "/placeholder.svg",
    tags: ["Next.js", "MDX", "Tailwind CSS"]
  },
  {
    id: 4,
    title: "天气预报应用",
    description: "使用OpenWeather API的实时天气应用",
    image: "/placeholder.svg",
    tags: ["React", "API集成", "CSS"]
  }
];

export const metadata = {
  title: "作品集 | 个人博客与作品集",
  description: "浏览我的项目作品集，了解我的技术能力和创意",
};

export default function PortfolioPage() {
  return (
    <div className="container py-8 md:py-12 space-y-8">
      <div className="space-y-4 text-center">
        <h1 className="text-4xl font-bold tracking-tight">作品集</h1>
        <p className="text-xl text-muted-foreground max-w-[700px] mx-auto">
          展示我的项目和作品，包括Web应用、网站和设计
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {projects.map((project) => (
          <Card key={project.id} className="overflow-hidden">
            <div className="aspect-video relative bg-muted">
              <Image 
                src={project.image} 
                alt={project.title}
                fill 
                className="object-cover" 
              />
            </div>
            <CardHeader>
              <CardTitle>{project.title}</CardTitle>
              <CardDescription>{project.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {project.tags.map((tag) => (
                  <span key={tag} className="inline-flex items-center rounded-md bg-muted px-2 py-1 text-xs font-medium">
                    {tag}
                  </span>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button asChild variant="ghost" className="p-0">
                <Link href={`/portfolio/${project.id}`}>查看详情 →</Link>
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}
