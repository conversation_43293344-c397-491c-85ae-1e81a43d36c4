import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

const featuredPosts = [
  {
    id: 1,
    title: "创建响应式网站的最佳实践",
    description: "学习如何使用现代技术栈构建响应式网站",
    date: "2024-04-20",
    category: "Web开发",
  },
  {
    id: 2,
    title: "Next.js 14的新特性",
    description: "探索Next.js最新版本中的新功能和改进",
    date: "2024-04-15",
    category: "前端框架",
  },
  {
    id: 3,
    title: "使用Tailwind CSS提高开发效率",
    description: "如何利用Tailwind CSS加速UI开发过程",
    date: "2024-04-10",
    category: "CSS",
  },
];

const featuredProjects = [
  {
    id: 1,
    title: "电子商务平台",
    description: "基于Next.js和Stripe构建的全功能电商网站",
    image: "/placeholder.svg",
    tags: ["Next.js", "React", "Stripe", "Tailwind CSS"],
  },
  {
    id: 2,
    title: "任务管理应用",
    description: "帮助团队协作和任务跟踪的Web应用",
    image: "/placeholder.svg",
    tags: ["React", "Firebase", "Tailwind CSS"],
  },
];

export default function Home() {
  return (
    <div className="container mx-auto px-4 py-8 md:py-12 space-y-16 w-full max-w-7xl">
      {/* 英雄区域 */}
      <section className="flex flex-col items-center text-center space-y-4 py-12 md:py-20 w-full">
        <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
          欢迎来到我的个人网站
        </h1>
        <p className="text-xl text-muted-foreground max-w-[700px] mx-auto">
          这里展示我的博客文章和项目作品集，分享我在Web开发领域的经验和见解。
        </p>
        <div className="flex gap-4 mt-6">
          <Button asChild size="lg">
            <Link href="/blog">浏览博客</Link>
          </Button>
          <Button asChild variant="outline" size="lg">
            <Link href="/portfolio">查看作品集</Link>
          </Button>
        </div>
      </section>

      {/* 精选博客文章 */}
      <section className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-3xl font-bold tracking-tight">精选文章</h2>
          <Button asChild variant="ghost">
            <Link href="/blog">查看全部 →</Link>
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {featuredPosts.map((post) => (
            <Card key={post.id} className="overflow-hidden">
              <CardHeader>
                <div className="text-sm text-muted-foreground">
                  {post.date} · {post.category}
                </div>
                <CardTitle className="line-clamp-2">{post.title}</CardTitle>
                <CardDescription className="line-clamp-2">
                  {post.description}
                </CardDescription>
              </CardHeader>
              <CardFooter>
                <Button asChild variant="ghost" className="p-0">
                  <Link href={`/blog/${post.id}`}>阅读全文 →</Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </section>

      {/* 精选项目 */}
      <section className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-3xl font-bold tracking-tight">精选项目</h2>
          <Button asChild variant="ghost">
            <Link href="/portfolio">查看全部 →</Link>
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {featuredProjects.map((project) => (
            <Card key={project.id} className="overflow-hidden">
              <div className="aspect-video relative bg-muted">
                <Image
                  src={project.image}
                  alt={project.title}
                  fill
                  className="object-cover"
                />
              </div>
              <CardHeader>
                <CardTitle>{project.title}</CardTitle>
                <CardDescription>{project.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {project.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center rounded-md bg-muted px-2 py-1 text-xs font-medium"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </CardContent>
              <CardFooter>
                <Button asChild variant="ghost" className="p-0">
                  <Link href={`/portfolio/${project.id}`}>查看详情 →</Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </section>
    </div>
  );
}
